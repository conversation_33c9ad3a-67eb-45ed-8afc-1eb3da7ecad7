<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">

         <title>{{ config('app.name', 'Laravel') }}</title>

        <meta name="csrf-token" content="{{ csrf_token() }}">

            <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />

        <!-- Styles / Scripts -->
        @if (file_exists(public_path('build/manifest.json')) || file_exists(public_path('hot')))
            @vite(['resources/css/app.css', 'resources/js/app.js'])
        @else
        @endif
    </head>

    <body class="bg-gray-50">

        <header class="bg-sky-800 p-4">
            <h1 class="text-white text-2xl font-bold">{{ config('app.name', 'Lara<PERSON>') }}</h1>
        </header>

        <main class="py-5">

            @yield('content')

        </main>

        <footer class="fixed bottom-0 w-full bg-sky-800 p-3 text-white text-center">
            <p class="text-sm">&copy; {{ date('Y') }} {{ config('app.name', 'Laravel') }}. </p>
        </footer>

    </body>
</html>
