@extends('layout.app')

@section('content')
<div class="flex items-start justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div class="bg-white rounded-lg shadow-lg p-8">
            <div class="text-center">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">
                    Con<PERSON>rma<PERSON> (CSRF Desabilitado)
                </h2>
                
                <!-- Formulário original -->
                <form method="POST" action="{{ route('guests.store') }}" class="space-y-6">
                    @csrf
                    <div id="names-container" class="space-y-4">
                        <div class="name-input-group">
                            <div class="flex space-x-2">
                                <input 
                                    type="text" 
                                    name="names[]" 
                                    id="name_0" 
                                    required
                                    class="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-sky-500 focus:border-sky-500"
                                    placeholder="Digite o nome completo"
                                >
                                
                            </div>
                            @error('names')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="flex space-x-4">
                        <button 
                            type="button" 
                            class="add-name-btn flex w-full bg-sky-600 hover:bg-sky-700 text-white px-3 py-2 rounded-md transition duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-sky-500 focus:ring-opacity-50"
                            title="Adicionar mais um nome"
                        >
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg> Adicionar pessoas
                        </button>
                    </div>

                    <div class="flex space-x-4">
                        <button 
                            type="submit" 
                            name="confirmed" 
                            value="1"
                            class="flex-1 bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 rounded-lg transition duration-200 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50"
                        >
                            Confirmar
                        </button>
                        
                        
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@endsection 