<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class GuestsController extends Controller
{
    /**
     * Mostra o formulário para confirmar presença
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('guests.create');
    }

    /**
     * Armazena a confirmação de presença
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // Debug temporário para verificar se está chegando aqui
        dd([
            'message' => 'Formulário recebido com sucesso!',
            'data' => $request->all(),
            'csrf_token' => $request->header('X-CSRF-TOKEN'),
            'session_id' => session()->getId(),
            'csrf_token_from_session' => csrf_token(),
            'has_token' => $request->has('_token'),
            'token_value' => $request->input('_token')
        ]);
    }
}
