// Importar Bootstrap JavaScript
import * as bootstrap from 'bootstrap';

// Importar estilos Sass
import '../sass/main.scss';

// Importar scripts customizados da pasta scripts
import '../scripts/app.js';

// Seus scripts customizados podem ser adicionados aqui
document.addEventListener('DOMContentLoaded', function() {
    console.log('Bootstrap carregado com sucesso!');

    // Inicializar app se disponível
    if (window.App && window.App.init) {
        window.App.init();
    }

    // Exemplo de inicialização de componentes Bootstrap
    // const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    // const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));
});