import { defineConfig } from 'vite';
import { glob } from 'glob';
import path from 'path';

export default defineConfig({
  root: '.',
  build: {
    outDir: 'public/dist',
    assetsDir: 'assets',
    minify: 'terser', // Minificação mais agressiva
    rollupOptions: {
      input: {
        // Entry point principal
        main: 'assets/js/main.js',
        // SASS principal
        styles: 'assets/sass/main.scss',
        // Todos os scripts da pasta scripts (se houver)
        ...Object.fromEntries(
          glob.sync('assets/scripts/**/*.js').map(file => [
            path.relative('assets/scripts', file.slice(0, file.length - path.extname(file).length)),
            file
          ])
        )
      },
      output: {
        entryFileNames: 'js/[name].min.js',
        chunkFileNames: 'js/[name].min.js',
        assetFileNames: (assetInfo) => {
          if (assetInfo.name.endsWith('.css')) {
            return 'css/[name].min.css';
          }
          return 'assets/[name][extname]';
        }
      }
    },
    manifest: true,
    sourcemap: false,
    cssMinify: true
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "bootstrap/scss/bootstrap";`
      }
    },
    // Configurações para minificação do CSS
    postcss: {
      plugins: []
    }
  },
  server: {
    port: 3000,
    open: false,
    cors: true
  },
  plugins: []
}); 