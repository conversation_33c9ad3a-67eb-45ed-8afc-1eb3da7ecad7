import { defineConfig } from 'vite';
import { glob } from 'glob';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

export default defineConfig({
  root: '.',
  build: {
    outDir: 'public/dist',
    assetsDir: 'assets',
    minify: 'terser', // Minificação mais agressiva
    rollupOptions: {
      input: {
        // Entry point principal que importa SASS e outros scripts
        main: path.resolve(__dirname, 'assets/js/main.js'),
        // Scripts adicionais da pasta scripts
        ...(() => {
          const scriptFiles = glob.sync('assets/scripts/**/*.js');
          const entries = {};
          scriptFiles.forEach(file => {
            const name = path.basename(file, '.js');
            entries[`scripts-${name}`] = path.resolve(__dirname, file);
          });
          return entries;
        })()
      },
      output: {
        entryFileNames: 'js/[name].min.js',
        chunkFileNames: 'js/[name].min.js',
        assetFileNames: (assetInfo) => {
          if (assetInfo.name.endsWith('.css')) {
            return 'css/[name].min.css';
          }
          return 'assets/[name][extname]';
        }
      }
    },
    manifest: true,
    sourcemap: false,
    cssMinify: true
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "bootstrap/scss/bootstrap";`
      }
    },
    // Configurações para minificação do CSS
    postcss: {
      plugins: []
    }
  },
  server: {
    port: 3000,
    open: false,
    cors: true
  },
  plugins: []
}); 