import { defineConfig } from 'vite';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

export default defineConfig({
  root: '.',
  build: {
    outDir: 'assets',
    emptyOutDir: false, // Não limpar a pasta assets
    minify: 'terser',
    rollupOptions: {
      input: {
        // SASS principal
        styles: path.resolve(__dirname, 'assets/sass/main.scss'),
        // Scripts da pasta scripts compilados juntos
        app: path.resolve(__dirname, 'assets/scripts/index.js')
      },
      output: {
        entryFileNames: (chunkInfo) => {
          if (chunkInfo.name === 'app') {
            return 'js/app.js';
          }
          return 'js/[name].js';
        },
        chunkFileNames: 'js/[name].js',
        assetFileNames: (assetInfo) => {
          if (assetInfo.name.endsWith('.css')) {
            return 'css/all.min.css';
          }
          return '[name][extname]';
        }
      }
    },
    manifest: false,
    sourcemap: false,
    cssMinify: true
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "bootstrap/scss/bootstrap";`
      }
    },
    // Configurações para minificação do CSS
    postcss: {
      plugins: []
    }
  },
  server: {
    port: 3000,
    open: false,
    cors: true
  },
  plugins: []
}); 