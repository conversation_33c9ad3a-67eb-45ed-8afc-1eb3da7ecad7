{"name": "guilhermenicola/rsvp-nicola", "type": "project", "description": "The skeleton application for the Laravel framework.", "license": "MIT", "autoload": {"psr-4": {"App\\": "src/", "Boot\\": "src/Boot/", "Helpers\\": "src/Helpers/"}, "files": ["src/Boot/Config.php"]}, "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>"}], "require": {"coffeecode/datalayer": "^2.0", "vlucas/phpdotenv": "^5.6", "league/plates": "^3.6", "altorouter/altorouter": "^2.0"}, "minimum-stability": "stable", "prefer-stable": true}