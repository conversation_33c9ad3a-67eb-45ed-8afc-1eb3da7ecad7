<?php 

require __DIR__ . '/vendor/autoload.php';

/**
 * BOOTSTRAP
 */
$router = new AltoRouter();
$router->setBasePath($_ENV['APP_URL']);

$router->map('GET', '/', [App\Controllers\HomeController::class, 'index'], 'home');


// match current request
$match = $router->match();


//ALLOW Class:class calls
if ($match && is_array($match['target']) && class_exists($match['target'][0])) {
    $controller = new $match['target'][0]; // Dynamically instantiate the controller class
    call_user_func([$controller, $match['target'][1]], ...array_values($match['params']));
}
else if( is_array($match) && is_callable( $match['target'] ) ) {
    call_user_func_array( $match['target'], $match['params'] );
} else {
    //var_dump($match);
    header( $_SERVER["SERVER_PROTOCOL"] . ' 404 Not Found');
}
