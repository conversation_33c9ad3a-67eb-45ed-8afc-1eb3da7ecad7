<?php 

require __DIR__ . '/vendor/autoload.php';

/**
 * BOOTSTRAP
 */
$router = new AltoRouter();
$router->setBasePath($_ENV['APP_URL']);



// HOME TEST
$router->addRoutes([
    array('GET', '/', [App\Controllers\HomeController::class, 'index'], 'home'),
    array('GET', 'confirm-form', [App\Controllers\HomeController::class, 'create'], 'create_confirm_form'),
    array('POST', 'confirm', [App\Controllers\HomeController::class, 'create'], 'confirm'),
]);
 

// match current request
$match = $router->match();


AltoRouter
PHP5.3+ Routing Class. Supports REST, dynamic and reversed routing.

View the Project on GitHub
dannyvankooten/AltoRouter

 Download ZIP  View on GitHub
Using AltoRouter
Processing Requests
AltoRouter does not process requests for you so you are free to use the method you prefer. To help you get started, here's a simplified example using closures.

$router = new AltoRouter();

// map homepage
$router->map( 'GET', '/', function() {
	require __DIR__ . '/views/home.php';
});

// map user details page
$router->map( 'GET', '/user/[i:id]/', function( $id ) {
	require __DIR__ . '/views/user-details.php';
});

// match current request url
$match = $router->match();

// call closure or throw 404 status
if( is_array($match) && is_callable( $match['target'] ) ) {
	call_user_func_array( $match['target'], $match['params'] );
} else {
	// no route was matched
	header( $_SERVER["SERVER_PROTOCOL"] . ' 404 Not Found');
}