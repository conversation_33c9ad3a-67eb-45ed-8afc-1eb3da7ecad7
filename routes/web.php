<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\GuestsController;
use App\Http\Middleware\DisableCsrf;

Route::get('/', function () {
    return view('welcome');
});

Route::get('/guests/create', [GuestsController::class, 'create'])->name('guests.create');
Route::post('/guests', [GuestsController::class, 'store'])->name('guests.store');

Route::get('/session-test', function () {
    session(['test' => 'it works!']);

    return session('test');
});
//Route::resource('guests', GuestsController::class)->only('create', 'store');